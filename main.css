* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.for-loop-container,
.while-loop-container,
.forEach-loop-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.for-loop-container{
    background-color: #f4f4f4;
}

.while-loop-container{
    background-color: #e0e0e0;
}

.forEach-loop-container{
    background-color: #d0d0d0;
}