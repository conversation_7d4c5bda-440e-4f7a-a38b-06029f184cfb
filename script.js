// For Loop Demonstration - Count from 1 to 10 with delay
function demonstrateForLoop() {
    const forLoopContainer = document.querySelector('.for-loop-container');

    // Create a div to display the numbers
    const numbersDisplay = document.createElement('div');
    numbersDisplay.className = 'numbers-display';
    numbersDisplay.style.fontSize = '24px';
    numbersDisplay.style.marginTop = '20px';
    numbersDisplay.style.padding = '10px';
    numbersDisplay.style.border = '2px solid #333';
    numbersDisplay.style.borderRadius = '5px';
    numbersDisplay.style.minHeight = '50px';
    numbersDisplay.innerHTML = 'Starting countdown...';

    forLoopContainer.appendChild(numbersDisplay);

    // Use setTimeout to create delay effect
    for (let i = 1; i <= 10; i++) {
        setTimeout(() => {
            if (i === 1) {
                numbersDisplay.innerHTML = `${i}`;
            } else {
                numbersDisplay.innerHTML += `, ${i}`;
            }

            // Add some visual feedback for the current number
            numbersDisplay.style.backgroundColor = i % 2 === 0 ? '#f0f8ff' : '#fff8f0';

            // When we reach 10, add completion message
            if (i === 10) {
                setTimeout(() => {
                    numbersDisplay.innerHTML += ' - Loop Complete! 🎉';
                    numbersDisplay.style.backgroundColor = '#e8f5e8';
                }, 100);
            }
        }, i * 500); // 500ms = 0.5 seconds delay for each number
    }
}

// Start the demonstration when the page loads
document.addEventListener('DOMContentLoaded', demonstrateForLoop);